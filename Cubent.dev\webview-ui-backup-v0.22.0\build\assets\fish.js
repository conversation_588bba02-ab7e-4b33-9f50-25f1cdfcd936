const e=Object.freeze(JSON.parse(`{"displayName":"Fish","fileTypes":["fish"],"firstLineMatch":"^#!.*\\\\bfish\\\\b","foldingStartMarker":"^\\\\s*(function|while|if|switch|for|begin)\\\\s.*$","foldingStopMarker":"^\\\\s*end\\\\s*$","name":"fish","patterns":[{"begin":"\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.fish"}},"end":"\\"","endCaptures":{"0":{"name":"punctuation.definition.string.end.fish"}},"name":"string.quoted.double.fish","patterns":[{"include":"#variable"},{"match":"\\\\\\\\([\\"$]|$|\\\\\\\\)","name":"constant.character.escape.fish"}]},{"begin":"'","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.fish"}},"end":"'","endCaptures":{"0":{"name":"punctuation.definition.string.end.fish"}},"name":"string.quoted.single.fish","patterns":[{"match":"\\\\\\\\(['\\\\\\\\\`])","name":"constant.character.escape.fish"}]},{"captures":{"1":{"name":"punctuation.definition.comment.fish"}},"match":"(?<!\\\\$)(#)(?!\\\\{).*$\\\\n?","name":"comment.line.number-sign.fish"},{"captures":{"2":{"name":"keyword.control.fish"},"4":{"name":"support.function.command.fish"}},"match":"(^\\\\s*|&&\\\\s*|\\\\|\\\\s*|\\\\(\\\\s*|;\\\\s*|\\\\b(if|while)\\\\b\\\\s+)(?!(?<!\\\\.)\\\\b(function|while|if|else|switch|case|for|in|begin|end|continue|break|return|source|exit|wait|and|or|not)\\\\b(?![!?]))([-\\\\].0-9A-\\\\[_a-z]+)"},{"match":"(?<!\\\\.)\\\\b(function|while|if|else|switch|case|for|in|begin|end|continue|break|return|source|exit|wait|and|or|not)\\\\b(?![!?])","name":"keyword.control.fish"},{"match":"(?<!\\\\.)\\\\bfunction\\\\b(?![!?])","name":"storage.type.fish"},{"match":"\\\\|","name":"keyword.operator.pipe.fish"},{"match":"<|([>^]|>>|\\\\^\\\\^)(&[-012])?|[012]([<>]|>>)(&[-012])?","name":"keyword.operator.redirect.fish"},{"match":"&","name":"keyword.operator.background.fish"},{"match":"\\\\*\\\\*|[*?]","name":"keyword.operator.glob.fish"},{"captures":{"1":{"name":"source.option.fish"}},"match":"\\\\s(-{1,2}[-0-9A-Z_a-z]+|-\\\\w)\\\\b"},{"include":"#variable"},{"include":"#escape"}],"repository":{"escape":{"patterns":[{"match":"\\\\\\\\[] \\"#$\\\\&-*;<>?\\\\[^abefnrtv{-~]","name":"constant.character.escape.single.fish"},{"match":"\\\\\\\\x\\\\h{1,2}","name":"constant.character.escape.hex-ascii.fish"},{"match":"\\\\\\\\X\\\\h{1,2}","name":"constant.character.escape.hex-byte.fish"},{"match":"\\\\\\\\[0-7]{1,3}","name":"constant.character.escape.octal.fish"},{"match":"\\\\\\\\u\\\\h{1,4}","name":"constant.character.escape.unicode-16-bit.fish"},{"match":"\\\\\\\\U\\\\h{1,8}","name":"constant.character.escape.unicode-32-bit.fish"},{"match":"\\\\\\\\c[A-Za-z]","name":"constant.character.escape.control.fish"}]},"variable":{"patterns":[{"captures":{"1":{"name":"punctuation.definition.variable.fish"}},"match":"(\\\\$)(argv|CMD_DURATION|COLUMNS|fish_bind_mode|fish_color_autosuggestion|fish_color_cancel|fish_color_command|fish_color_comment|fish_color_cwd|fish_color_cwd_root|fish_color_end|fish_color_error|fish_color_escape|fish_color_hg_added|fish_color_hg_clean|fish_color_hg_copied|fish_color_hg_deleted|fish_color_hg_dirty|fish_color_hg_modified|fish_color_hg_renamed|fish_color_hg_unmerged|fish_color_hg_untracked|fish_color_history_current|fish_color_host|fish_color_host_remote|fish_color_match|fish_color_normal|fish_color_operator|fish_color_param|fish_color_quote|fish_color_redirection|fish_color_search_match|fish_color_selection|fish_color_status|fish_color_user|fish_color_valid_path|fish_complete_path|fish_function_path|fish_greeting|fish_key_bindings|fish_pager_color_completion|fish_pager_color_description|fish_pager_color_prefix|fish_pager_color_progress|fish_pid|fish_prompt_hg_status_added|fish_prompt_hg_status_copied|fish_prompt_hg_status_deleted|fish_prompt_hg_status_modified|fish_prompt_hg_status_order|fish_prompt_hg_status_unmerged|fish_prompt_hg_status_untracked|FISH_VERSION|history|hostname|IFS|LINES|pipestatus|status|umask|version)\\\\b","name":"variable.language.fish"},{"captures":{"1":{"name":"punctuation.definition.variable.fish"}},"match":"(\\\\$)[A-Z_a-z][0-9A-Z_a-z]*","name":"variable.other.normal.fish"}]}},"scopeName":"source.fish"}`)),i=[e];export{i as default};
//# sourceMappingURL=fish.js.map
