import e from"./javascript.js";import a from"./typescript.js";import n from"./jsx.js";import l from"./tsx.js";const r=Object.freeze(JSON.parse(`{"displayName":"GraphQL","fileTypes":["graphql","graphqls","gql","graphcool"],"name":"graphql","patterns":[{"include":"#graphql"}],"repository":{"graphql":{"patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-fragment-definition"},{"include":"#graphql-directive-definition"},{"include":"#graphql-type-interface"},{"include":"#graphql-enum"},{"include":"#graphql-scalar"},{"include":"#graphql-union"},{"include":"#graphql-schema"},{"include":"#graphql-operation-def"},{"include":"#literal-quasi-embedded"}]},"graphql-ampersand":{"captures":{"1":{"name":"keyword.operator.logical.graphql"}},"match":"\\\\s*(&)"},"graphql-arguments":{"begin":"\\\\s*(\\\\()","beginCaptures":{"1":{"name":"meta.brace.round.directive.graphql"}},"end":"\\\\s*(\\\\))","endCaptures":{"1":{"name":"meta.brace.round.directive.graphql"}},"name":"meta.arguments.graphql","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"begin":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*(:)","beginCaptures":{"1":{"name":"variable.parameter.graphql"},"2":{"name":"punctuation.colon.graphql"}},"end":"(?=\\\\s*(?:([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*(:)|\\\\)))|\\\\s*(,)","endCaptures":{"3":{"name":"punctuation.comma.graphql"}},"patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-directive"},{"include":"#graphql-value"},{"include":"#graphql-skip-newlines"}]},{"include":"#literal-quasi-embedded"}]},"graphql-boolean-value":{"captures":{"1":{"name":"constant.language.boolean.graphql"}},"match":"\\\\s*\\\\b(true|false)\\\\b"},"graphql-colon":{"captures":{"1":{"name":"punctuation.colon.graphql"}},"match":"\\\\s*(:)"},"graphql-comma":{"captures":{"1":{"name":"punctuation.comma.graphql"}},"match":"\\\\s*(,)"},"graphql-comment":{"patterns":[{"captures":{"1":{"name":"punctuation.whitespace.comment.leading.graphql"}},"match":"(\\\\s*)(#).*","name":"comment.line.graphql.js"},{"begin":"(\\"\\"\\")","beginCaptures":{"1":{"name":"punctuation.whitespace.comment.leading.graphql"}},"end":"(\\"\\"\\")","name":"comment.line.graphql.js"},{"begin":"(\\")","beginCaptures":{"1":{"name":"punctuation.whitespace.comment.leading.graphql"}},"end":"(\\")","name":"comment.line.graphql.js"}]},"graphql-description-docstring":{"begin":"\\"\\"\\"","end":"\\"\\"\\"","name":"comment.block.graphql"},"graphql-description-singleline":{"match":"#(?=([^\\"]*\\"[^\\"]*\\")*[^\\"]*$).*$","name":"comment.line.number-sign.graphql"},"graphql-directive":{"applyEndPatternLast":1,"begin":"\\\\s*((@)\\\\s*([A-Z_a-z][0-9A-Z_a-z]*))","beginCaptures":{"1":{"name":"entity.name.function.directive.graphql"}},"end":"(?=.)","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-arguments"},{"include":"#literal-quasi-embedded"},{"include":"#graphql-skip-newlines"}]},"graphql-directive-definition":{"applyEndPatternLast":1,"begin":"\\\\s*\\\\b(directive)\\\\b\\\\s*(@[A-Z_a-z][0-9A-Z_a-z]*)","beginCaptures":{"1":{"name":"keyword.directive.graphql"},"2":{"name":"entity.name.function.directive.graphql"},"3":{"name":"keyword.on.graphql"},"4":{"name":"support.type.graphql"}},"end":"(?=.)","patterns":[{"include":"#graphql-variable-definitions"},{"applyEndPatternLast":1,"begin":"\\\\s*\\\\b(on)\\\\b\\\\s*([A-Z_a-z]*)","beginCaptures":{"1":{"name":"keyword.on.graphql"},"2":{"name":"support.type.location.graphql"}},"end":"(?=.)","patterns":[{"include":"#graphql-skip-newlines"},{"include":"#graphql-comment"},{"include":"#literal-quasi-embedded"},{"captures":{"2":{"name":"support.type.location.graphql"}},"match":"\\\\s*(\\\\|)\\\\s*([A-Z_a-z]*)"}]},{"include":"#graphql-skip-newlines"},{"include":"#graphql-comment"},{"include":"#literal-quasi-embedded"}]},"graphql-enum":{"begin":"\\\\s*+\\\\b(enum)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)","beginCaptures":{"1":{"name":"keyword.enum.graphql"},"2":{"name":"support.type.enum.graphql"}},"end":"(?<=})","name":"meta.enum.graphql","patterns":[{"begin":"\\\\s*(\\\\{)","beginCaptures":{"1":{"name":"punctuation.operation.graphql"}},"end":"\\\\s*(})","endCaptures":{"1":{"name":"punctuation.operation.graphql"}},"name":"meta.type.object.graphql","patterns":[{"include":"#graphql-object-type"},{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-directive"},{"include":"#graphql-enum-value"},{"include":"#literal-quasi-embedded"}]},{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-directive"}]},"graphql-enum-value":{"match":"\\\\s*(?!=\\\\b(true|false|null)\\\\b)([A-Z_a-z][0-9A-Z_a-z]*)","name":"constant.character.enum.graphql"},"graphql-field":{"patterns":[{"captures":{"1":{"name":"string.unquoted.alias.graphql"},"2":{"name":"punctuation.colon.graphql"}},"match":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*(:)"},{"captures":{"1":{"name":"variable.graphql"}},"match":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)"},{"include":"#graphql-arguments"},{"include":"#graphql-directive"},{"include":"#graphql-selection-set"},{"include":"#literal-quasi-embedded"},{"include":"#graphql-skip-newlines"}]},"graphql-float-value":{"captures":{"1":{"name":"constant.numeric.float.graphql"}},"match":"\\\\s*(-?(0|[1-9][0-9]*)(\\\\.[0-9]+)?(([Ee])([-+])?[0-9]+)?)"},"graphql-fragment-definition":{"begin":"\\\\s*\\\\b(fragment)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)?\\\\s*\\\\b(on)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)","captures":{"1":{"name":"keyword.fragment.graphql"},"2":{"name":"entity.name.fragment.graphql"},"3":{"name":"keyword.on.graphql"},"4":{"name":"support.type.graphql"}},"end":"(?<=})","name":"meta.fragment.graphql","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-selection-set"},{"include":"#graphql-directive"},{"include":"#graphql-skip-newlines"},{"include":"#literal-quasi-embedded"}]},"graphql-fragment-spread":{"applyEndPatternLast":1,"begin":"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?!\\\\bon\\\\b)([A-Z_a-z][0-9A-Z_a-z]*)","captures":{"1":{"name":"keyword.operator.spread.graphql"},"2":{"name":"variable.fragment.graphql"}},"end":"(?=.)","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-selection-set"},{"include":"#graphql-directive"},{"include":"#literal-quasi-embedded"},{"include":"#graphql-skip-newlines"}]},"graphql-ignore-spaces":{"match":"\\\\s*"},"graphql-inline-fragment":{"applyEndPatternLast":1,"begin":"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?:\\\\b(on)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*))?","captures":{"1":{"name":"keyword.operator.spread.graphql"},"2":{"name":"keyword.on.graphql"},"3":{"name":"support.type.graphql"}},"end":"(?=.)","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-selection-set"},{"include":"#graphql-directive"},{"include":"#graphql-skip-newlines"},{"include":"#literal-quasi-embedded"}]},"graphql-input-types":{"patterns":[{"include":"#graphql-scalar-type"},{"captures":{"1":{"name":"support.type.graphql"},"2":{"name":"keyword.operator.nulltype.graphql"}},"match":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)(?:\\\\s*(!))?"},{"begin":"\\\\s*(\\\\[)","captures":{"1":{"name":"meta.brace.square.graphql"},"2":{"name":"keyword.operator.nulltype.graphql"}},"end":"\\\\s*(])(?:\\\\s*(!))?","name":"meta.type.list.graphql","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-input-types"},{"include":"#graphql-comma"},{"include":"#literal-quasi-embedded"}]}]},"graphql-list-value":{"patterns":[{"begin":"\\\\s*+(\\\\[)","beginCaptures":{"1":{"name":"meta.brace.square.graphql"}},"end":"\\\\s*(])","endCaptures":{"1":{"name":"meta.brace.square.graphql"}},"name":"meta.listvalues.graphql","patterns":[{"include":"#graphql-value"}]}]},"graphql-name":{"captures":{"1":{"name":"entity.name.function.graphql"}},"match":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)"},"graphql-null-value":{"captures":{"1":{"name":"constant.language.null.graphql"}},"match":"\\\\s*\\\\b(null)\\\\b"},"graphql-object-field":{"captures":{"1":{"name":"constant.object.key.graphql"},"2":{"name":"string.unquoted.graphql"},"3":{"name":"punctuation.graphql"}},"match":"\\\\s*(([A-Z_a-z][0-9A-Z_a-z]*))\\\\s*(:)"},"graphql-object-value":{"patterns":[{"begin":"\\\\s*+(\\\\{)","beginCaptures":{"1":{"name":"meta.brace.curly.graphql"}},"end":"\\\\s*(})","endCaptures":{"1":{"name":"meta.brace.curly.graphql"}},"name":"meta.objectvalues.graphql","patterns":[{"include":"#graphql-object-field"},{"include":"#graphql-value"}]}]},"graphql-operation-def":{"patterns":[{"include":"#graphql-query-mutation"},{"include":"#graphql-name"},{"include":"#graphql-variable-definitions"},{"include":"#graphql-directive"},{"include":"#graphql-selection-set"}]},"graphql-query-mutation":{"captures":{"1":{"name":"keyword.operation.graphql"}},"match":"\\\\s*\\\\b(query|mutation)\\\\b"},"graphql-scalar":{"captures":{"1":{"name":"keyword.scalar.graphql"},"2":{"name":"entity.scalar.graphql"}},"match":"\\\\s*\\\\b(scalar)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)"},"graphql-scalar-type":{"captures":{"1":{"name":"support.type.builtin.graphql"},"2":{"name":"keyword.operator.nulltype.graphql"}},"match":"\\\\s*\\\\b(Int|Float|String|Boolean|ID)\\\\b(?:\\\\s*(!))?"},"graphql-schema":{"begin":"\\\\s*\\\\b(schema)\\\\b","beginCaptures":{"1":{"name":"keyword.schema.graphql"}},"end":"(?<=})","patterns":[{"begin":"\\\\s*(\\\\{)","beginCaptures":{"1":{"name":"punctuation.operation.graphql"}},"end":"\\\\s*(})","endCaptures":{"1":{"name":"punctuation.operation.graphql"}},"patterns":[{"begin":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\s*\\\\(|:)","beginCaptures":{"1":{"name":"variable.arguments.graphql"}},"end":"(?=\\\\s*(([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*([(:])|(})))|\\\\s*(,)","endCaptures":{"5":{"name":"punctuation.comma.graphql"}},"patterns":[{"captures":{"1":{"name":"support.type.graphql"}},"match":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)"},{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-colon"},{"include":"#graphql-skip-newlines"}]},{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-skip-newlines"}]},{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-directive"},{"include":"#graphql-skip-newlines"}]},"graphql-selection-set":{"begin":"\\\\s*(\\\\{)","beginCaptures":{"1":{"name":"punctuation.operation.graphql"}},"end":"\\\\s*(})","endCaptures":{"1":{"name":"punctuation.operation.graphql"}},"name":"meta.selectionset.graphql","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-field"},{"include":"#graphql-fragment-spread"},{"include":"#graphql-inline-fragment"},{"include":"#graphql-comma"},{"include":"#native-interpolation"},{"include":"#literal-quasi-embedded"}]},"graphql-skip-newlines":{"match":"\\\\s*\\\\n"},"graphql-string-content":{"patterns":[{"match":"\\\\\\\\[\\"'/\\\\\\\\bfnrt]","name":"constant.character.escape.graphql"},{"match":"\\\\\\\\u(\\\\h{4})","name":"constant.character.escape.graphql"}]},"graphql-string-value":{"begin":"\\\\s*+((\\"))","beginCaptures":{"1":{"name":"string.quoted.double.graphql"},"2":{"name":"punctuation.definition.string.begin.graphql"}},"contentName":"string.quoted.double.graphql","end":"\\\\s*+(?:((\\"))|(\\\\n))","endCaptures":{"1":{"name":"string.quoted.double.graphql"},"2":{"name":"punctuation.definition.string.end.graphql"},"3":{"name":"invalid.illegal.newline.graphql"}},"patterns":[{"include":"#graphql-string-content"},{"include":"#literal-quasi-embedded"}]},"graphql-type-definition":{"begin":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\s*\\\\(|:)","beginCaptures":{"1":{"name":"variable.graphql"}},"end":"(?=\\\\s*(([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*([(:])|(})))|\\\\s*(,)","endCaptures":{"5":{"name":"punctuation.comma.graphql"}},"patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-directive"},{"include":"#graphql-variable-definitions"},{"include":"#graphql-type-object"},{"include":"#graphql-colon"},{"include":"#graphql-input-types"},{"include":"#literal-quasi-embedded"}]},"graphql-type-interface":{"applyEndPatternLast":1,"begin":"\\\\s*\\\\b(?:(extends?)?\\\\b\\\\s*\\\\b(type)|(interface)|(input))\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)?","captures":{"1":{"name":"keyword.type.graphql"},"2":{"name":"keyword.type.graphql"},"3":{"name":"keyword.interface.graphql"},"4":{"name":"keyword.input.graphql"},"5":{"name":"support.type.graphql"}},"end":"(?=.)","name":"meta.type.interface.graphql","patterns":[{"begin":"\\\\s*\\\\b(implements)\\\\b\\\\s*","beginCaptures":{"1":{"name":"keyword.implements.graphql"}},"end":"\\\\s*(?=\\\\{)","patterns":[{"captures":{"1":{"name":"support.type.graphql"}},"match":"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)"},{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-directive"},{"include":"#graphql-ampersand"},{"include":"#graphql-comma"}]},{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-directive"},{"include":"#graphql-type-object"},{"include":"#literal-quasi-embedded"},{"include":"#graphql-ignore-spaces"}]},"graphql-type-object":{"begin":"\\\\s*(\\\\{)","beginCaptures":{"1":{"name":"punctuation.operation.graphql"}},"end":"\\\\s*(})","endCaptures":{"1":{"name":"punctuation.operation.graphql"}},"name":"meta.type.object.graphql","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-object-type"},{"include":"#graphql-type-definition"},{"include":"#literal-quasi-embedded"}]},"graphql-union":{"applyEndPatternLast":1,"begin":"\\\\s*\\\\b(union)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)","captures":{"1":{"name":"keyword.union.graphql"},"2":{"name":"support.type.graphql"}},"end":"(?=.)","patterns":[{"applyEndPatternLast":1,"begin":"\\\\s*(=)\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)","captures":{"1":{"name":"punctuation.assignment.graphql"},"2":{"name":"support.type.graphql"}},"end":"(?=.)","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-skip-newlines"},{"include":"#literal-quasi-embedded"},{"captures":{"1":{"name":"punctuation.or.graphql"},"2":{"name":"support.type.graphql"}},"match":"\\\\s*(\\\\|)\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)"}]},{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-skip-newlines"},{"include":"#literal-quasi-embedded"}]},"graphql-union-mark":{"captures":{"1":{"name":"punctuation.union.graphql"}},"match":"\\\\s*(\\\\|)"},"graphql-value":{"patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-variable-name"},{"include":"#graphql-float-value"},{"include":"#graphql-string-value"},{"include":"#graphql-boolean-value"},{"include":"#graphql-null-value"},{"include":"#graphql-enum-value"},{"include":"#graphql-list-value"},{"include":"#graphql-object-value"},{"include":"#literal-quasi-embedded"}]},"graphql-variable-assignment":{"applyEndPatternLast":1,"begin":"\\\\s(=)","beginCaptures":{"1":{"name":"punctuation.assignment.graphql"}},"end":"(?=[\\\\n),])","patterns":[{"include":"#graphql-value"}]},"graphql-variable-definition":{"begin":"\\\\s*(\\\\$?[A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\s*\\\\(|:)","beginCaptures":{"1":{"name":"variable.parameter.graphql"}},"end":"(?=\\\\s*((\\\\$?[A-Z_a-z][0-9A-Z_a-z]*)\\\\s*([(:])|([)}])))|\\\\s*(,)","endCaptures":{"5":{"name":"punctuation.comma.graphql"}},"name":"meta.variables.graphql","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-directive"},{"include":"#graphql-colon"},{"include":"#graphql-input-types"},{"include":"#graphql-variable-assignment"},{"include":"#literal-quasi-embedded"},{"include":"#graphql-skip-newlines"}]},"graphql-variable-definitions":{"begin":"\\\\s*(\\\\()","captures":{"1":{"name":"meta.brace.round.graphql"}},"end":"\\\\s*(\\\\))","patterns":[{"include":"#graphql-comment"},{"include":"#graphql-description-docstring"},{"include":"#graphql-description-singleline"},{"include":"#graphql-variable-definition"},{"include":"#literal-quasi-embedded"}]},"graphql-variable-name":{"captures":{"1":{"name":"variable.graphql"}},"match":"\\\\s*(\\\\$[A-Z_a-z][0-9A-Z_a-z]*)"},"native-interpolation":{"begin":"\\\\s*(\\\\$\\\\{)","beginCaptures":{"1":{"name":"keyword.other.substitution.begin"}},"end":"(})","endCaptures":{"1":{"name":"keyword.other.substitution.end"}},"name":"native.interpolation","patterns":[{"include":"source.js"},{"include":"source.ts"},{"include":"source.js.jsx"},{"include":"source.tsx"}]}},"scopeName":"source.graphql","embeddedLangs":["javascript","typescript","jsx","tsx"],"aliases":["gql"]}`)),c=[...e,...a,...n,...l,r];export{c as default};
//# sourceMappingURL=graphql.js.map
