
> @cubent/vscode-webview@ lint C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui
> eslint src --ext=ts,tsx --max-warnings=0


C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\chat\ChatRow.tsx
    5:24  warning  'VSCodeProgressRing' is defined but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
   20:10  warning  'Button' is defined but never used. Allowed unused vars must match /^_/u                          @typescript-eslint/no-unused-vars
   28:10  warning  'ReasoningBlock' is defined but never used. Allowed unused vars must match /^_/u                  @typescript-eslint/no-unused-vars
   31:8   warning  'McpToolRow' is defined but never used. Allowed unused vars must match /^_/u                      @typescript-eslint/no-unused-vars
  127:22  warning  'alwaysAllowMcp' is assigned a value but never used. Allowed unused vars must match /^_/u         @typescript-eslint/no-unused-vars
  127:38  warning  'currentCheckpoint' is assigned a value but never used. Allowed unused vars must match /^_/u      @typescript-eslint/no-unused-vars
  128:9   warning  'reasoningCollapsed' is assigned a value but never used. Allowed unused vars must match /^_/u     @typescript-eslint/no-unused-vars
  128:29  warning  'setReasoningCollapsed' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  358:11  warning  'isAppending' is assigned a value but never used. Allowed unused vars must match /^_/u            @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\chat\ChatTextArea.tsx
   17:2   warning  'insertMention' is defined but never used. Allowed unused vars must match /^_/u                                        @typescript-eslint/no-unused-vars
   25:8   warning  'Thumbnails' is defined but never used. Allowed unused vars must match /^_/u                                           @typescript-eslint/no-unused-vars
   32:8   warning  'AutoApproveToggleButton' is defined but never used. Allowed unused vars must match /^_/u                              @typescript-eslint/no-unused-vars
   85:4   warning  'selectedModel' is assigned a value but never used. Allowed unused args must match /^_/u                               @typescript-eslint/no-unused-vars
   86:4   warning  'onModelChange' is assigned a value but never used. Allowed unused args must match /^_/u                               @typescript-eslint/no-unused-vars
   87:4   warning  'onModelSettingsClick' is assigned a value but never used. Allowed unused args must match /^_/u                        @typescript-eslint/no-unused-vars
  178:10  warning  'isFocused' is assigned a value but never used. Allowed unused vars must match /^_/u                                   @typescript-eslint/no-unused-vars
  392:11  warning  'imagesToSend' is assigned a value but never used. Allowed unused vars must match /^_/u                                @typescript-eslint/no-unused-vars
  629:13  warning  'imageContexts' is assigned a value but never used. Allowed unused vars must match /^_/u                               @typescript-eslint/no-unused-vars
  696:4   warning  React Hook useCallback has a missing dependency: 'selectedContexts'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\chat\ChatView.tsx
     7:10  warning  'VSCodeButton' is defined but never used. Allowed unused vars must match /^_/u                                       @typescript-eslint/no-unused-vars
   139:9   warning  'didClickCancel' is assigned a value but never used. Allowed unused vars must match /^_/u                            @typescript-eslint/no-unused-vars
  1227:3   warning  React Hook useCallback has a missing dependency: 'isAutoApproved'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\chat\CommandExecution.tsx
   3:10  warning  'ChevronDown' is defined but never used. Allowed unused vars must match /^_/u   @typescript-eslint/no-unused-vars
   3:23  warning  'Skull' is defined but never used. Allowed unused vars must match /^_/u         @typescript-eslint/no-unused-vars
   5:10  warning  'VSCodeButton' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  31:55  warning  'icon' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  31:61  warning  'title' is defined but never used. Allowed unused args must match /^_/u         @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\chat\DiffSummaryBar.tsx
   30:30  warning  'index' is defined but never used. Allowed unused args must match /^_/u           @typescript-eslint/no-unused-vars
  153:14  warning  'error' is defined but never used. Allowed unused caught errors must match /^_/u  @typescript-eslint/no-unused-vars
  272:33  warning  'index' is defined but never used. Allowed unused args must match /^_/u           @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\chat\Markdown.tsx
  5:43  warning  'partial' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\chat\ModelSelector.tsx
  28:10  warning  't' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\chat\TaskHeader.tsx
    4:10  warning  'VSCodeBadge' is defined but never used. Allowed unused vars must match /^_/u                  @typescript-eslint/no-unused-vars
    5:10  warning  'CloudUpload' is defined but never used. Allowed unused vars must match /^_/u                  @typescript-eslint/no-unused-vars
    5:23  warning  'CloudDownload' is defined but never used. Allowed unused vars must match /^_/u                @typescript-eslint/no-unused-vars
    9:10  warning  'getModelMaxOutputTokens' is defined but never used. Allowed unused vars must match /^_/u      @typescript-eslint/no-unused-vars
   11:10  warning  'formatLargeNumber' is defined but never used. Allowed unused vars must match /^_/u            @typescript-eslint/no-unused-vars
   18:8   warning  'Thumbnails' is defined but never used. Allowed unused vars must match /^_/u                   @typescript-eslint/no-unused-vars
   20:10  warning  'ChatActions' is defined but never used. Allowed unused vars must match /^_/u                  @typescript-eslint/no-unused-vars
   21:10  warning  'ContextWindowProgress' is defined but never used. Allowed unused vars must match /^_/u        @typescript-eslint/no-unused-vars
   43:2   warning  'tokensIn' is defined but never used. Allowed unused args must match /^_/u                     @typescript-eslint/no-unused-vars
   44:2   warning  'tokensOut' is defined but never used. Allowed unused args must match /^_/u                    @typescript-eslint/no-unused-vars
   45:2   warning  'doesModelSupportPromptCache' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
   46:2   warning  'cacheWrites' is defined but never used. Allowed unused args must match /^_/u                  @typescript-eslint/no-unused-vars
   47:2   warning  'cacheReads' is defined but never used. Allowed unused args must match /^_/u                   @typescript-eslint/no-unused-vars
   48:2   warning  'totalCost' is defined but never used. Allowed unused args must match /^_/u                    @typescript-eslint/no-unused-vars
   49:2   warning  'contextTokens' is defined but never used. Allowed unused args must match /^_/u                @typescript-eslint/no-unused-vars
   56:14  warning  'modelId' is assigned a value but never used. Allowed unused vars must match /^_/u             @typescript-eslint/no-unused-vars
   74:8   warning  'handleTogglePin' is assigned a value but never used. Allowed unused vars must match /^_/u     @typescript-eslint/no-unused-vars
   78:8   warning  'textContainerRef' is assigned a value but never used. Allowed unused vars must match /^_/u    @typescript-eslint/no-unused-vars
   79:8   warning  'textRef' is assigned a value but never used. Allowed unused vars must match /^_/u             @typescript-eslint/no-unused-vars
   80:8   warning  'contextWindow' is assigned a value but never used. Allowed unused vars must match /^_/u       @typescript-eslint/no-unused-vars
   82:17  warning  'windowWidth' is assigned a value but never used. Allowed unused vars must match /^_/u         @typescript-eslint/no-unused-vars
   84:8   warning  'condenseButton' is assigned a value but never used. Allowed unused vars must match /^_/u      @typescript-eslint/no-unused-vars
  153:16  warning  'isPinned' is assigned a value but never used. Allowed unused vars must match /^_/u            @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\common\CodeAccordian.tsx
   2:10  warning  'VSCodeProgressRing' is defined but never used. Allowed unused vars must match /^_/u                   @typescript-eslint/no-unused-vars
  84:5   warning  React Hook useMemo has a missing dependency: 'path'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  86:8   warning  'diffStatsText' is assigned a value but never used. Allowed unused vars must match /^_/u               @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\common\CodeBlock.tsx
  138:7  warning  'HeaderDots' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  143:7  warning  'HeaderDot' is assigned a value but never used. Allowed unused vars must match /^_/u   @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\common\StatusDot.tsx
  11:8  warning  'getColorClasses' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\history\useChatSearch.ts
  68:6  warning  Unexpected lexical declaration in case block  no-case-declarations
  69:6  warning  Unexpected lexical declaration in case block  no-case-declarations

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\settings\SettingsView.tsx
   16:2   warning  'SquareMousePointer' is defined but never used. Allowed unused vars must match /^_/u               @typescript-eslint/no-unused-vars
   18:2   warning  'Bell' is defined but never used. Allowed unused vars must match /^_/u                             @typescript-eslint/no-unused-vars
   20:2   warning  'SquareTerminal' is defined but never used. Allowed unused vars must match /^_/u                   @typescript-eslint/no-unused-vars
   21:2   warning  'FlaskConical' is defined but never used. Allowed unused vars must match /^_/u                     @typescript-eslint/no-unused-vars
   23:2   warning  'Globe' is defined but never used. Allowed unused vars must match /^_/u                            @typescript-eslint/no-unused-vars
   25:2   warning  'MessageSquare' is defined but never used. Allowed unused vars must match /^_/u                    @typescript-eslint/no-unused-vars
   61:10  warning  'BrowserSettings' is defined but never used. Allowed unused vars must match /^_/u                  @typescript-eslint/no-unused-vars
   63:10  warning  'NotificationSettings' is defined but never used. Allowed unused vars must match /^_/u             @typescript-eslint/no-unused-vars
   66:10  warning  'TerminalSettings' is defined but never used. Allowed unused vars must match /^_/u                 @typescript-eslint/no-unused-vars
   67:10  warning  'ExperimentalSettings' is defined but never used. Allowed unused vars must match /^_/u             @typescript-eslint/no-unused-vars
   68:10  warning  'LanguageSettings' is defined but never used. Allowed unused vars must match /^_/u                 @typescript-eslint/no-unused-vars
   71:8   warning  'PromptsSettings' is defined but never used. Allowed unused vars must match /^_/u                  @typescript-eslint/no-unused-vars
  109:72  warning  'onDone' is defined but never used. Allowed unused args must match /^_/u                           @typescript-eslint/no-unused-vars
  178:3   warning  'maxConcurrentFileReads' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  182:3   warning  'codebaseIndexModels' is assigned a value but never used. Allowed unused vars must match /^_/u     @typescript-eslint/no-unused-vars
  249:8   warning  'setExperimentEnabled' is assigned a value but never used. Allowed unused vars must match /^_/u    @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\ui\select-dropdown.tsx
   3:17  warning  'X' is defined but never used. Allowed unused vars must match /^_/u                       @typescript-eslint/no-unused-vars
  68:12  warning  't' is assigned a value but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
  98:10  warning  'onClearSearch' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\user\TrialBanner.tsx
  111:12  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\user\UsageDisplay.tsx
   49:10  warning  't' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  289:58  warning  'index' is defined but never used. Allowed unused args must match /^_/u       @typescript-eslint/no-unused-vars
  323:12  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`               react/no-unescaped-entities

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui\src\components\user\UserManagementSettings.tsx
   9:2   warning  'CreditCard' is defined but never used. Allowed unused vars must match /^_/u       @typescript-eslint/no-unused-vars
  66:10  warning  't' is assigned a value but never used. Allowed unused vars must match /^_/u       @typescript-eslint/no-unused-vars
  71:9   warning  'saving' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

✖ 89 problems (0 errors, 89 warnings)

ESLint found too many warnings (maximum: 0).
 ELIFECYCLE  Command failed with exit code 1.
