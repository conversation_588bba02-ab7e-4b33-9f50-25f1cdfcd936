const a=Object.freeze(JSON.parse(`{"displayName":"WebAssembly","name":"wasm","patterns":[{"include":"#comments"},{"include":"#strings"},{"include":"#instructions"},{"include":"#types"},{"include":"#modules"},{"include":"#constants"},{"include":"#invalid"}],"repository":{"comments":{"patterns":[{"captures":{"1":{"name":"punctuation.definition.comment.wat"}},"match":"(;;).*$","name":"comment.line.wat"},{"begin":"\\\\(;","beginCaptures":{"0":{"name":"punctuation.definition.comment.wat"}},"end":";\\\\)","endCaptures":{"0":{"name":"punctuation.definition.comment.wat"}},"name":"comment.block.wat"}]},"constants":{"patterns":[{"patterns":[{"captures":{"1":{"name":"support.type.wat"}},"match":"\\\\b(i8x16)(?:\\\\s+0x\\\\h{1,2}){16}\\\\b","name":"constant.numeric.vector.wat"},{"captures":{"1":{"name":"support.type.wat"}},"match":"\\\\b(i16x8)(?:\\\\s+0x\\\\h{1,4}){8}\\\\b","name":"constant.numeric.vector.wat"},{"captures":{"1":{"name":"support.type.wat"}},"match":"\\\\b(i32x4)(?:\\\\s+0x\\\\h{1,8}){4}\\\\b","name":"constant.numeric.vector.wat"},{"captures":{"1":{"name":"support.type.wat"}},"match":"\\\\b(i64x2)(?:\\\\s+0x\\\\h{1,16}){2}\\\\b","name":"constant.numeric.vector.wat"}]},{"patterns":[{"match":"[-+]?\\\\b[0-9][0-9]*(?:\\\\.[0-9][0-9]*)?(?:[Ee][-+]?[0-9]+)?\\\\b","name":"constant.numeric.float.wat"},{"match":"[-+]?\\\\b0x(\\\\h*\\\\.\\\\h+|\\\\h+\\\\.?)[Pp][-+]?[0-9]+\\\\b","name":"constant.numeric.float.wat"},{"match":"[-+]?\\\\binf\\\\b","name":"constant.numeric.float.wat"},{"match":"[-+]?\\\\bnan:0x\\\\h\\\\h*\\\\b","name":"constant.numeric.float.wat"},{"match":"[-+]?\\\\b(?:0x\\\\h\\\\h*|\\\\d\\\\d*)\\\\b","name":"constant.numeric.integer.wat"}]}]},"instructions":{"patterns":[{"patterns":[{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i(?:32|64))\\\\.trunc_sat_f(?:32|64)_[su]\\\\b","name":"keyword.operator.word.wat"}]},{"patterns":[{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i32)\\\\.extend(?:8|16)_s\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i64)\\\\.extend(?:8|16|32)_s\\\\b","name":"keyword.operator.word.wat"}]},{"patterns":[{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(memory)\\\\.(?:copy|fill|init|drop)\\\\b","name":"keyword.operator.word.wat"}]},{"patterns":[{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(v128)\\\\.(?:const|and|or|xor|not|andnot|bitselect|load|store)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i8x16)\\\\.(?:shuffle|swizzle|splat|replace_lane|add|sub|mul|neg|shl|shr_[su]|eq|ne|lt_[su]|le_[su]|gt_[su]|ge_[su]|min_[su]|max_[su]|any_true|all_true|extract_lane_[su]|add_saturate_[su]|sub_saturate_[su]|avgr_u|narrow_i16x8_[su])\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i16x8)\\\\.(?:splat|replace_lane|add|sub|mul|neg|shl|shr_[su]|eq|ne|lt_[su]|le_[su]|gt_[su]|ge_[su]|min_[su]|max_[su]|any_true|all_true|extract_lane_[su]|add_saturate_[su]|sub_saturate_[su]|avgr_u|load8x8_[su]|narrow_i32x4_[su]|widen_(low|high)_i8x16_[su])\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i32x4)\\\\.(?:splat|replace_lane|add|sub|mul|neg|shl|shr_[su]|eq|ne|lt_[su]|le_[su]|gt_[su]|ge_[su]|min_[su]|max_[su]|any_true|all_true|extract_lane|load16x4_[su]|trunc_sat_f32x4_[su]|widen_(low|high)_i16x8_[su])\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i64x2)\\\\.(?:splat|replace_lane|add|sub|mul|neg|shl|shr_[su]|extract_lane|load32x2_[su])\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(f32x4)\\\\.(?:splat|replace_lane|add|sub|mul|neg|extract_lane|eq|ne|lt|le|gt|ge|abs|min|max|div|sqrt|convert_i32x4_[su])\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(f64x2)\\\\.(?:splat|replace_lane|add|sub|mul|neg|extract_lane|eq|ne|lt|le|gt|ge|abs|min|max|div|sqrt)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(v8x16)\\\\.(?:load_splat|shuffle|swizzle)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(v16x8)\\\\.load_splat\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(v32x4)\\\\.load_splat\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(v64x2)\\\\.load_splat\\\\b","name":"keyword.operator.word.wat"}]},{"patterns":[{"captures":{"1":{"name":"support.class.wat"},"2":{"name":"support.class.wat"},"3":{"name":"support.class.wat"},"4":{"name":"support.class.wat"}},"match":"\\\\b(i32)\\\\.(atomic)\\\\.(?:load(?:8_u|16_u)?|store(?:8|16)?|wait|(rmw)\\\\.(?:add|sub|and|or|xor|xchg|cmpxchg)|(rmw(?:8|16))\\\\.(?:add|sub|and|or|xor|xchg|cmpxchg)_u)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"},"2":{"name":"support.class.wat"},"3":{"name":"support.class.wat"},"4":{"name":"support.class.wat"}},"match":"\\\\b(i64)\\\\.(atomic)\\\\.(?:load(?:(?:8|16|32)_u)?|store(?:8|16|32)?|wait|(rmw)\\\\.(?:add|sub|and|or|xor|xchg|cmpxchg)|(rmw(?:8|16|32))\\\\.(?:add|sub|and|or|xor|xchg|cmpxchg)_u)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(atomic)\\\\.(?:notify|fence)\\\\b","name":"keyword.operator.word.wat"},{"match":"\\\\bshared\\\\b","name":"storage.modifier.wat"}]},{"patterns":[{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(ref)\\\\.(?:null|is_null|func|extern)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(table)\\\\.(?:get|size|grow|fill|init|copy)\\\\b","name":"keyword.operator.word.wat"},{"match":"\\\\b(?:extern|func|null)ref\\\\b","name":"entity.name.type.wat"}]},{"patterns":[{"match":"\\\\breturn_call(?:_indirect)?\\\\b","name":"keyword.control.wat"}]},{"patterns":[{"match":"\\\\b(?:try|catch|throw|rethrow|br_on_exn)\\\\b","name":"keyword.control.wat"},{"match":"(?<=\\\\()event\\\\b","name":"storage.type.wat"}]},{"patterns":[{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i32|i64|f32|f64|externref|funcref|nullref|exnref)\\\\.p(?:ush|op)\\\\b","name":"keyword.operator.word.wat"}]},{"patterns":[{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(i32)\\\\.(?:load|load(?:8|16)(?:_[su])?|store(?:8|16)?)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(i64)\\\\.(?:load|load(?:8|16|32)(?:_[su])?|store(?:8|16|32)?)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(f(?:32|64))\\\\.(?:load|store)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.memory.wat"}},"match":"\\\\b(memory)\\\\.(?:size|grow)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"entity.other.attribute-name.wat"}},"match":"\\\\b(offset|align)=\\\\b"},{"captures":{"1":{"name":"support.class.local.wat"}},"match":"\\\\b(local)\\\\.(?:get|set|tee)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.global.wat"}},"match":"\\\\b(global)\\\\.[gs]et\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(i(?:32|64))\\\\.(const|eqz?|ne|lt_[su]|gt_[su]|le_[su]|ge_[su]|clz|ctz|popcnt|add|sub|mul|div_[su]|rem_[su]|and|or|xor|shl|shr_[su]|rotl|rotr)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(f(?:32|64))\\\\.(const|eq|ne|lt|gt|le|ge|abs|neg|ceil|floor|trunc|nearest|sqrt|add|sub|mul|div|min|max|copysign)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(i32)\\\\.(wrap_i64|trunc_(f(?:32|64))_[su]|reinterpret_f32)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(i64)\\\\.(extend_i32_[su]|trunc_f(32|64)_[su]|reinterpret_f64)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(f32)\\\\.(convert_i(32|64)_[su]|demote_f64|reinterpret_i32)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.type.wat"}},"match":"\\\\b(f64)\\\\.(convert_i(32|64)_[su]|promote_f32|reinterpret_i64)\\\\b","name":"keyword.operator.word.wat"},{"match":"\\\\b(?:unreachable|nop|block|loop|if|then|else|end|br|br_if|br_table|return|call|call_indirect)\\\\b","name":"keyword.control.wat"},{"match":"\\\\b(?:drop|select)\\\\b","name":"keyword.operator.word.wat"}]},{"patterns":[{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(ref)\\\\.(?:eq|test|cast)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(struct)\\\\.(?:new_canon|new_canon_default|get|get_s|get_u|set)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(array)\\\\.(?:new_canon|new_canon_default|get|get_s|get_u|set|len|new_canon_fixed|new_canon_data|new_canon_elem)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(i31)\\\\.(?:new|get_s|get_u)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\bbr_on_(?:non_null|cast|cast_fail)\\\\b","name":"keyword.operator.word.wat"},{"captures":{"1":{"name":"support.class.wat"}},"match":"\\\\b(extern)\\\\.(?:in|ex)ternalize\\\\b","name":"keyword.operator.word.wat"}]}]},"invalid":{"patterns":[{"match":"[^()\\\\s]+","name":"invalid.wat"}]},"modules":{"patterns":[{"patterns":[{"captures":{"1":{"name":"storage.modifier.wat"}},"match":"(?<=\\\\(data)\\\\s+(passive)\\\\b"}]},{"patterns":[{"match":"(?<=\\\\()(?:module|import|export|memory|data|table|elem|start|func|type|param|result|global|local)\\\\b","name":"storage.type.wat"},{"captures":{"1":{"name":"storage.modifier.wat"}},"match":"(?<=\\\\()\\\\s*(mut)\\\\b","name":"storage.modifier.wat"},{"captures":{"1":{"name":"entity.name.function.wat"}},"match":"(?<=\\\\(func|\\\\(start|call|return_call|ref\\\\.func)\\\\s+(\\\\$[!#-'*+\\\\--:<-Z\\\\\\\\^-z|~]*)"},{"begin":"\\\\)\\\\s+(\\\\$[!#-'*+\\\\--:<-Z\\\\\\\\^-z|~]*)","beginCaptures":{"1":{"name":"entity.name.function.wat"}},"end":"\\\\)","patterns":[{"match":"(?<=\\\\s)\\\\$[!#-'*+\\\\--:<-Z\\\\\\\\^-z|~]*","name":"entity.name.function.wat"}]},{"captures":{"1":{"name":"support.type.function.wat"}},"match":"(?<=\\\\(type)\\\\s+(\\\\$[!#-'*+\\\\--:<-Z\\\\\\\\^-z|~]*)"},{"match":"\\\\$[!#-'*+\\\\--:<-Z\\\\\\\\^-z|~]*\\\\b","name":"variable.other.wat"}]}]},"strings":{"begin":"\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin"}},"end":"\\"","endCaptures":{"0":{"name":"punctuation.definition.string.end"}},"name":"string.quoted.double.wat","patterns":[{"match":"\\\\\\\\([\\"'\\\\\\\\nt]|\\\\h{2})","name":"constant.character.escape.wat"}]},"types":{"patterns":[{"patterns":[{"match":"\\\\bv128\\\\b(?!\\\\.)","name":"entity.name.type.wat"}]},{"patterns":[{"match":"\\\\b(?:extern|func|null)ref\\\\b(?!\\\\.)","name":"entity.name.type.wat"}]},{"patterns":[{"match":"\\\\bexnref\\\\b(?!\\\\.)","name":"entity.name.type.wat"}]},{"patterns":[{"match":"\\\\b(?:i32|i64|f32|f64)\\\\b(?!\\\\.)","name":"entity.name.type.wat"}]},{"patterns":[{"match":"\\\\b(?:i8|i16|ref|funcref|externref|anyref|eqref|i31ref|nullfuncref|nullexternref|structref|arrayref|nullref)\\\\b(?!\\\\.)","name":"entity.name.type.wat"}]},{"patterns":[{"match":"\\\\b(?:type|func|extern|any|eq|nofunc|noextern|struct|array|none)\\\\b(?!\\\\.)","name":"entity.name.type.wat"}]},{"patterns":[{"match":"\\\\b(?:struct|array|sub|final|rec|field|mut)\\\\b(?!\\\\.)","name":"entity.name.type.wat"}]}]}},"scopeName":"source.wat"}`)),t=[a];export{t as default};
//# sourceMappingURL=wasm.js.map
